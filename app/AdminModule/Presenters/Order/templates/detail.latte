{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\Order\Order $order}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $translator->translate('Orders') . ': ' . $order->orderNumber,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}
		<p class="u-mb-xs">
			<b>Datum objednávky:</b>
			{$order->placedAt|date:'d.m.Y H:i:s'} |
			Stav noviko: {ifset $novikoStatusName}{$novikoStatusName}{else}{/ifset}
		</p>
		{control orderForm}
		{control orderItemsForm}
		{if $order->isActionAllowed(App\Model\Orm\Order\Order::ORDER_ACTION_CANCEL)}
	  {control orderCancelForm}
		{/if}

		{var $props = [
			title: 'Historie',
			id: 'orderHistory',
			icon: $templates.'/part/icons/align-left.svg',
			variant: 'main',
			open: true,
			classes: ['u-mb-xxs'],
			rowMain: false
		]}

		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				<table>
					<thead>
						<tr>
							<th>Datum a čas</th>
							<th>Původní stav</th>
							<th>Nový stav</th>
							<th>Důvod</th>
						</tr>
					</thead>
					<tbody>
						<tr n:foreach="$order->stateChanges as $stateChange">
							<td>{$stateChange->changedAt|date:'j.n.Y H:i'}</td>
							<td>{$stateChange->from?->value}</td>
							<td>{$stateChange->to->value}</td>
							<td>{$stateChange->reason ?? ''}</td>
						</tr>
					</tbody>
				</table>
			{/block}
		{/embed}

	</div>
</div>
