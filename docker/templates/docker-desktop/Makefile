PROJECT_NAME = omega

DATE := $(shell date +%Y-%m-%d_%H-%M-%S)
BRANCH := $(shell git rev-parse --abbrev-ref HEAD | sed 's/\//-/g')

DB_IMPORT ?= ./dump.sql
DB_EXPORT ?= ./dump_$(BRANCH)_$(DATE).sql

all: up
.PHONY: all up stop install install-php install-front install-admin install-admin-new build-front build-admin build-admin-new migrate migrations-reset xdebug-on xdebug-off create-migration

ROOT_DIR := $(strip $(shell dirname "$(realpath $(firstword $(MAKEFILE_LIST)))"))

up:
	docker compose up -d


stop:
	docker compose stop

install: install-php install-front install-admin install-admin-new

install-php: up
	docker compose run --rm -it app php composer.phar install

install-front: up
	docker compose run --rm -it front npm install

install-admin: src/admin/package.json src/admin/package-lock.json
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npm install

install-admin-new: up
	docker compose run --rm -it admin npm install

build: build-front build-admin build-admin-new

build-front: install-front
	docker compose run --rm -it front npm run build

build-admin: install-admin
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:10 npx gulp

build-admin-new: install-admin-new
	docker compose run --rm -it admin npm run build

migrate: install-php
	docker compose run --rm -it app php bin/console migrations:continue

migrations-reset: install-php
	docker compose run --rm -it app php bin/console migrations:reset

populate-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:create -psc

xdebug-on:
	SUPERADMIN_XDEBUG=on docker compose up --force-recreate --no-deps -d app

xdebug-off:
	SUPERADMIN_XDEBUG=off docker compose up --force-recreate --no-deps -d app

clear-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:purge -f

redis-clear-front:
	docker exec -i $(PROJECT_NAME)_redis sh -c "redis-cli -n 2 FLUSHDB"
redis-clear-storage:
	docker exec -i $(PROJECT_NAME)_redis sh -c "redis-cli -n 0 FLUSHDB"

supervisor-start:
	docker exec -i $(PROJECT_NAME)_app sh -c "service supervisor start"
supervisor-stop:
	docker exec -i $(PROJECT_NAME)_app sh -c "service supervisor stop"
supervisor-reload:
	docker exec -i $(PROJECT_NAME)_app sh -c "supervisorctl reread && supervisorctl update"
supervisor-status:
	docker exec -i $(PROJECT_NAME)_app sh -c "supervisorctl status"


import-db:
	docker exec -i $(PROJECT_NAME)_db mysql -u root -proot $(PROJECT_NAME) < $(DB_IMPORT)
export-db:
	docker exec -i $(PROJECT_NAME)_db sh -c 'exec mysqldump -uroot -proot $(PROJECT_NAME)' > $(DB_EXPORT)
php-console:
	docker exec -it $(PROJECT_NAME)_app bash
qa:
	docker compose run --rm -it app sh -c "\
                           php composer.phar phpstan && \
                           php composer.phar cs-fix && \
                           php composer.phar cs && \
                           php composer.phar tests"

cypress:
	docker-compose exec cypress npx cypress open

create-migration:
	@if [ -z "$(NAMESPACE)" ] || [ -z "$(DESC)" ]; then \
    		echo ""; \
    		echo "❌ Chýbajúce parametre."; \
    		echo "Použitie:"; \
    		echo "  make create-migration NAMESPACE=NazovNamespace DESC=\"Nazov migracie\""; \
    		echo ""; \
    		echo "Príklad:"; \
    		echo "  make create-migration NAMESPACE=core_s DESC=\"added another column\""; \
			echo ""; \
    		exit 1; \
	fi
	@echo "✅ Vytváram migráciu $(NAMESPACE) ($(DESC))..."
	docker exec -it dronpro_app php bin/console migrations:create "$(NAMESPACE)" "$(DESC)"

clean-cache: redis-clear-storage
	docker exec -i $(PROJECT_NAME)_app sh -c 'sh cleanCache.sh'

after-db-switch: clean-cache supervisor-stop migrate clear-elastic populate-elastic supervisor-start




